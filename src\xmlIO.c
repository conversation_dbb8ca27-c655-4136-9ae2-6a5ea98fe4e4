/*
 * xmlIO.c : 解析器使用的I/O接口的实现
 *
 *
 *
 *
 */

#define IN_LIBXML
#include "libxml.h"

#include <string.h>
#include <stdlib.h>
#include <errno.h>

#include <fcntl.h>
#include <sys/stat.h>

#if defined(_WIN32)
  #define WIN32_LEAN_AND_MEAN
  #include <windows.h>
  #include <io.h>
  #include <direct.h>
#else
  #include <unistd.h>
#endif

#ifdef LIBXML_ZLIB_ENABLED
#include <zlib.h>
#endif
#ifdef LIBXML_LZMA_ENABLED
#include <lzma.h>
#endif

#include <libxml/xmlIO.h>
#include <libxml/xmlmemory.h>
#include <libxml/uri.h>
#include <libxml/parserInternals.h>
#include <libxml/xmlerror.h>
#ifdef LIBXML_CATALOG_ENABLED
#include <libxml/catalog.h>
#endif

#include "private/buf.h"
#include "private/enc.h"
#include "private/entities.h"
#include "private/error.h"
#include "private/io.h"

#ifndef SIZE_MAX
  #define SIZE_MAX ((size_t) -1)
#endif

/* #define VERBOSE_FAILURE */

#define MINLEN 4000

#ifndef STDOUT_FILENO
  #define STDOUT_FILENO 1
#endif

#ifndef S_ISDIR
#  ifdef _S_ISDIR
#    define S_ISDIR(x) _S_ISDIR(x)
#  elif defined(S_IFDIR)
#    ifdef S_IFMT
#      define S_ISDIR(m) (((m) & S_IFMT) == S_IFDIR)
#    elif defined(_S_IFMT)
#      define S_ISDIR(m) (((m) & _S_IFMT) == S_IFDIR)
#    endif
#  endif
#endif

/*
 * 输入I/O回调集合
 */
typedef struct _xmlInputCallback {
    xmlInputMatchCallback matchcallback;
    xmlInputOpenCallback opencallback;
    xmlInputReadCallback readcallback;
    xmlInputCloseCallback closecallback;
} xmlInputCallback;

/* 这个虚拟函数仅在回调表中标记默认IO */
static int
xmlIODefaultMatch(const char *filename);

#define MAX_INPUT_CALLBACK 10

static xmlInputCallback xmlInputCallbackTable[MAX_INPUT_CALLBACK];
static int xmlInputCallbackNr;

#ifdef LIBXML_OUTPUT_ENABLED
/*
 * 输出I/O回调集合
 */
typedef struct _xmlOutputCallback {
    xmlOutputMatchCallback matchcallback;
    xmlOutputOpenCallback opencallback;
    xmlOutputWriteCallback writecallback;
    xmlOutputCloseCallback closecallback;
} xmlOutputCallback;

#define MAX_OUTPUT_CALLBACK 10

static xmlOutputCallback xmlOutputCallbackTable[MAX_OUTPUT_CALLBACK];
static int xmlOutputCallbackNr;
#endif /* LIBXML_OUTPUT_ENABLED */

/************************************************************************
 *									*
 *			错误处理					*
 *									*
 ************************************************************************/

/**
 * 将errno转换为xmlParserErrors。
 *
 * @param err  错误号
 * @returns xmlParserErrors代码。
 */
static xmlParserErrors
xmlIOErr(int err)
{
    xmlParserErrors code;

    switch (err) {
#ifdef EACCES
        case EACCES: code = XML_IO_EACCES; break;
#endif
#ifdef EAGAIN
        case EAGAIN: code = XML_IO_EAGAIN; break;
#endif
#ifdef EBADF
        case EBADF: code = XML_IO_EBADF; break;
#endif
#ifdef EBADMSG
        case EBADMSG: code = XML_IO_EBADMSG; break;
#endif
#ifdef EBUSY
        case EBUSY: code = XML_IO_EBUSY; break;
#endif
#ifdef ECANCELED
        case ECANCELED: code = XML_IO_ECANCELED; break;
#endif
#ifdef ECHILD
        case ECHILD: code = XML_IO_ECHILD; break;
#endif
#ifdef EDEADLK
        case EDEADLK: code = XML_IO_EDEADLK; break;
#endif
#ifdef EDOM
        case EDOM: code = XML_IO_EDOM; break;
#endif
#ifdef EEXIST
        case EEXIST: code = XML_IO_EEXIST; break;
#endif
#ifdef EFAULT
        case EFAULT: code = XML_IO_EFAULT; break;
#endif
#ifdef EFBIG
        case EFBIG: code = XML_IO_EFBIG; break;
#endif
#ifdef EINPROGRESS
        case EINPROGRESS: code = XML_IO_EINPROGRESS; break;
#endif
#ifdef EINTR
        case EINTR: code = XML_IO_EINTR; break;
#endif
#ifdef EINVAL
        case EINVAL: code = XML_IO_EINVAL; break;
#endif
#ifdef EIO
        case EIO: code = XML_IO_EIO; break;
#endif
#ifdef EISDIR
        case EISDIR: code = XML_IO_EISDIR; break;
#endif
#ifdef EMFILE
        case EMFILE: code = XML_IO_EMFILE; break;
#endif
#ifdef EMLINK
        case EMLINK: code = XML_IO_EMLINK; break;
#endif
#ifdef EMSGSIZE
        case EMSGSIZE: code = XML_IO_EMSGSIZE; break;
#endif
#ifdef ENAMETOOLONG
        case ENAMETOOLONG: code = XML_IO_ENAMETOOLONG; break;
#endif
#ifdef ENFILE
        case ENFILE: code = XML_IO_ENFILE; break;
#endif
#ifdef ENODEV
        case ENODEV: code = XML_IO_ENODEV; break;
#endif
#ifdef ENOENT
        case ENOENT: code = XML_IO_ENOENT; break;
#endif
#ifdef ENOEXEC
        case ENOEXEC: code = XML_IO_ENOEXEC; break;
#endif
#ifdef ENOLCK
        case ENOLCK: code = XML_IO_ENOLCK; break;
#endif
#ifdef ENOMEM
        case ENOMEM: code = XML_IO_ENOMEM; break;
#endif
#ifdef ENOSPC
        case ENOSPC: code = XML_IO_ENOSPC; break;
#endif
#ifdef ENOSYS
        case ENOSYS: code = XML_IO_ENOSYS; break;
#endif
#ifdef ENOTDIR
        case ENOTDIR: code = XML_IO_ENOTDIR; break;
#endif
#ifdef ENOTEMPTY
        case ENOTEMPTY: code = XML_IO_ENOTEMPTY; break;
#endif
#ifdef ENOTSUP
        case ENOTSUP: code = XML_IO_ENOTSUP; break;
#endif
#ifdef ENOTTY
        case ENOTTY: code = XML_IO_ENOTTY; break;
#endif
#ifdef ENXIO
        case ENXIO: code = XML_IO_ENXIO; break;
#endif
#ifdef EPERM
        case EPERM: code = XML_IO_EPERM; break;
#endif
#ifdef EPIPE
        case EPIPE: code = XML_IO_EPIPE; break;
#endif
#ifdef ERANGE
        case ERANGE: code = XML_IO_ERANGE; break;
#endif
#ifdef EROFS
        case EROFS: code = XML_IO_EROFS; break;
#endif
#ifdef ESPIPE
        case ESPIPE: code = XML_IO_ESPIPE; break;
#endif
#ifdef ESRCH
        case ESRCH: code = XML_IO_ESRCH; break;
#endif
#ifdef ETIMEDOUT
        case ETIMEDOUT: code = XML_IO_ETIMEDOUT; break;
#endif
#ifdef EXDEV
        case EXDEV: code = XML_IO_EXDEV; break;
#endif
#ifdef ENOTSOCK
        case ENOTSOCK: code = XML_IO_ENOTSOCK; break;
#endif
#ifdef EISCONN
        case EISCONN: code = XML_IO_EISCONN; break;
#endif
#ifdef ECONNREFUSED
        case ECONNREFUSED: code = XML_IO_ECONNREFUSED; break;
#endif
#ifdef ENETUNREACH
        case ENETUNREACH: code = XML_IO_ENETUNREACH; break;
#endif
#ifdef EADDRINUSE
        case EADDRINUSE: code = XML_IO_EADDRINUSE; break;
#endif
#ifdef EALREADY
        case EALREADY: code = XML_IO_EALREADY; break;
#endif
#ifdef EAFNOSUPPORT
        case EAFNOSUPPORT: code = XML_IO_EAFNOSUPPORT; break;
#endif
        default: code = XML_IO_UNKNOWN; break;
    }

    return(code);
}

/************************************************************************
 *									*
 *		文件访问的标准I/O				*
 *									*
 ************************************************************************/

#if defined(_WIN32)

/**
 * 将字符串从utf-8转换为wchar（仅限WINDOWS！）
 *
 * @param u8String  utf-8字符串
 */
static wchar_t *
__xmlIOWin32UTF8ToWChar(const char *u8String)
{
    wchar_t *wString = NULL;
    int i;

    if (u8String) {
        int wLen =
            MultiByteToWideChar(CP_UTF8, MB_ERR_INVALID_CHARS, u8String,
                                -1, NULL, 0);
        if (wLen) {
            wString = xmlMalloc(wLen * sizeof(wchar_t));
            if (wString) {
                if (MultiByteToWideChar
                    (CP_UTF8, 0, u8String, -1, wString, wLen) == 0) {
                    xmlFree(wString);
                    wString = NULL;
                }
            }

            /*
             * 转换为反斜杠
             */
            for (i = 0; wString[i] != 0; i++) {
                if (wString[i] == '/')
                    wString[i] = '\\';
            }
        }
    }

    return wString;
}

#endif

/**
 * @deprecated 这从未真正起作用。
 *
 * @param path  输入文件路径
 * @returns path的副本。
 */
xmlChar *
xmlNormalizeWindowsPath(const xmlChar *path)
{
    return xmlStrdup(path);
}

/**
 * 如果目标机器上没有stat可用，
 *
 * @deprecated 内部函数，请勿使用。
 *
 * @param path  要检查的路径
 * @returns 1。如果stat失败，返回0（如果对文件名调用
 * stat失败，则不可能是正确的）。
 * 如果stat成功且文件是目录，
 * @returns 2。否则返回1。
 */
int
xmlCheckFilename(const char *path)
{
#if defined(_WIN32)
    struct _stat stat_buffer;
#else
    struct stat stat_buffer;
#endif
    int res;

    if (path == NULL)
	return(0);

#if defined(_WIN32)
    {
        wchar_t *wpath;

        /*
         * 在Windows上，stat和wstat不能处理长路径名，
         * 这些路径名以'\\?\'开头
         */
        if ((path[0] == '\\') && (path[1] == '\\') && (path[2] == '?') &&
            (path[3] == '\\') )
                return 1;

        wpath = __xmlIOWin32UTF8ToWChar(path);
        if (wpath == NULL)
            return(0);
        res = _wstat(wpath, &stat_buffer);
        xmlFree(wpath);
    }
#else
    res = stat(path, &stat_buffer);
#endif

    if (res < 0)
        return 0;

#ifdef S_ISDIR
    if (S_ISDIR(stat_buffer.st_mode))
        return 2;
#endif

    return 1;
}

static int
xmlConvertUriToPath(const char *uri, char **out) {
    const char *escaped;
    char *unescaped;

    *out = NULL;

    if (!xmlStrncasecmp(BAD_CAST uri, BAD_CAST "file://localhost/", 17)) {
	escaped = &uri[16];
    } else if (!xmlStrncasecmp(BAD_CAST uri, BAD_CAST "file:///", 8)) {
	escaped = &uri[7];
    } else if (!xmlStrncasecmp(BAD_CAST uri, BAD_CAST "file:/", 6)) {
        /* 许多生成器似乎懒得阅读RFC 1738 */
	escaped = &uri[5];
    } else {
        return(1);
    }

#ifdef _WIN32
    /* 忽略斜杠，如file:///C:/file.txt中的斜杠 */
    escaped += 1;
#endif

    unescaped = xmlURIUnescapeString(escaped, 0, NULL);
    if (unescaped == NULL)
        return(-1);

    *out = unescaped;
    return(0);
}

typedef struct {
    int fd;
} xmlFdIOCtxt;

/**
 * @param filename  用于匹配的URI
 * @param write  fd是否为写入而打开
 * @param out  指向结果上下文的指针
 * @returns xmlParserErrors代码
 */
static xmlParserErrors
xmlFdOpen(const char *filename, int write, int *out) {
    char *fromUri = NULL;
    int flags;
    int fd;
    xmlParserErrors ret;

    *out = -1;
    if (filename == NULL)
        return(XML_ERR_ARGUMENT);

    if (xmlConvertUriToPath(filename, &fromUri) < 0)
        return(XML_ERR_NO_MEMORY);

    if (fromUri != NULL)
        filename = fromUri;

#if defined(_WIN32)
    {
        wchar_t *wpath;

        wpath = __xmlIOWin32UTF8ToWChar(filename);
        if (wpath == NULL) {
            xmlFree(fromUri);
            return(XML_ERR_NO_MEMORY);
        }
        if (write)
            flags = _O_WRONLY | _O_CREAT | _O_TRUNC;
        else
            flags = _O_RDONLY;
	fd = _wopen(wpath, flags | _O_BINARY, 0666);
        xmlFree(wpath);
    }
#else
    if (write)
        flags = O_WRONLY | O_CREAT | O_TRUNC;
    else
        flags = O_RDONLY;
    fd = open(filename, flags, 0666);
#endif /* WIN32 */

    if (fd < 0) {
        /*
         * Windows和可能的其他平台对无效文件名
         * 返回EINVAL。
         */
        if ((errno == ENOENT) || (errno == EINVAL)) {
            ret = XML_IO_ENOENT;
        } else {
            ret = xmlIOErr(errno);
        }
    } else {
        *out = fd;
        ret = XML_ERR_OK;
    }

    xmlFree(fromUri);
    return(ret);
}

/**
 * 从I/O通道读取`len`字节到`buffer`。
 *
 * @param context  I/O上下文
 * @param buffer  数据存放位置
 * @param len  要读取的字节数
 * @returns 读取的字节数
 */
static int
xmlFdRead(void *context, char *buffer, int len) {
    xmlFdIOCtxt *fdctxt = context;
    int fd = fdctxt->fd;
    int ret = 0;
    int bytes;

    while (len > 0) {
        bytes = read(fd, buffer, len);
        if (bytes < 0) {
            /*
             * 如果我们已经获得了一些字节，返回它们而不
             * 引发错误。
             */
            if (ret > 0)
                break;
            return(-xmlIOErr(errno));
        }
        if (bytes == 0)
            break;
        ret += bytes;
        buffer += bytes;
        len -= bytes;
    }

    return(ret);
}

#ifdef LIBXML_OUTPUT_ENABLED
/**
 * 将`buffer`中的`len`字节写入I/O通道。
 *
 * @param context  I/O上下文
 * @param buffer  数据来源位置
 * @param len  要写入的字节数
 * @returns 写入的字节数
 */
static int
xmlFdWrite(void *context, const char *buffer, int len) {
    xmlFdIOCtxt *fdctxt = context;
    int fd = fdctxt->fd;
    int ret = 0;
    int bytes;

    while (len > 0) {
	bytes = write(fd, buffer, len);
	if (bytes < 0)
            return(-xmlIOErr(errno));
        ret += bytes;
        buffer += bytes;
        len -= bytes;
    }

    return(ret);
}
#endif /* LIBXML_OUTPUT_ENABLED */

static int
xmlFdFree(void *context) {
    xmlFree(context);
    return(XML_ERR_OK);
}

/**
 * 关闭I/O通道
 *
 * @param context  I/O上下文
 * @returns 成功时返回0，否则返回错误代码
 */
static int
xmlFdClose (void * context) {
    xmlFdIOCtxt *fdctxt = context;
    int fd = fdctxt->fd;
    int ret;

    ret = close(fd);

    xmlFree(fdctxt);

    if (ret < 0)
        return(xmlIOErr(errno));

    return(XML_ERR_OK);
}

/**
 * @deprecated 内部函数，请勿使用。
 *
 * @param filename  用于匹配的URI
 * @returns 匹配时返回1，否则返回0
 */
int
xmlFileMatch (const char *filename ATTRIBUTE_UNUSED) {
    return(1);
}

/**
 * 从FILE *输入
 *
 * @param filename  用于匹配的URI
 * @param write  文件是否为写入而打开
 * @param out  指向结果上下文的指针
 * @returns xmlParserErrors代码
 */
static xmlParserErrors
xmlFileOpenSafe(const char *filename, int write, void **out) {
    char *fromUri = NULL;
    FILE *fd;
    xmlParserErrors ret = XML_ERR_OK;

    *out = NULL;
    if (filename == NULL)
        return(XML_ERR_ARGUMENT);

    if (xmlConvertUriToPath(filename, &fromUri) < 0)
        return(XML_ERR_NO_MEMORY);

    if (fromUri != NULL)
        filename = fromUri;

#if defined(_WIN32)
    {
        wchar_t *wpath;

        wpath = __xmlIOWin32UTF8ToWChar(filename);
        if (wpath == NULL) {
            xmlFree(fromUri);
            return(XML_ERR_NO_MEMORY);
        }
	fd = _wfopen(wpath, write ? L"wb" : L"rb");
        xmlFree(wpath);
    }
#else
    fd = fopen(filename, write ? "wb" : "rb");
#endif /* WIN32 */

    if (fd == NULL) {
        /*
         * Windows和可能的其他平台对无效文件名
         * 返回EINVAL。
         */
        if ((errno == ENOENT) || (errno == EINVAL)) {
            ret = XML_IO_ENOENT;
        } else {
            /*
             * 此错误不会转发到解析器上下文，
             * 解析器上下文会第二次报告它。
             */
            ret = xmlIOErr(errno);
        }
    }

    *out = fd;
    xmlFree(fromUri);
    return(ret);
}

/**
 * @deprecated 内部函数，请勿使用。
 *
 * @param filename  用于匹配的URI
 * @returns IO上下文，失败时返回NULL
 */
void *
xmlFileOpen(const char *filename) {
    void *context;

    xmlFileOpenSafe(filename, 0, &context);
    return(context);
}

/**
 * @deprecated 内部函数，请勿使用。
 *
 * @param context  I/O上下文
 * @param buffer  数据存放位置
 * @param len  要写入的字节数
 * @returns 读取的字节数，失败时返回< 0
 */
int
xmlFileRead(void * context, char * buffer, int len) {
    FILE *file = context;
    size_t bytes;

    if ((context == NULL) || (buffer == NULL))
        return(-1);

    /*
     * C标准不要求fread设置errno，只有
     * POSIX要求。Windows文档不是很清楚。
     * 将errno设置为零，如果fread失败而没有设置errno，
     * 将报告为未知错误。
     */
    errno = 0;
    bytes = fread(buffer, 1, len, file);
    if ((bytes < (size_t) len) && (ferror(file)))
        return(-xmlIOErr(errno));

    return(bytes);
}

#ifdef LIBXML_OUTPUT_ENABLED
/**
 * 将`buffer`中的`len`字节写入I/O通道。
 *
 * @param context  I/O上下文
 * @param buffer  数据存放位置
 * @param len  要写入的字节数
 * @returns 写入的字节数
 */
static int
xmlFileWrite(void *context, const char *buffer, int len) {
    FILE *file = context;
    size_t bytes;

    if ((context == NULL) || (buffer == NULL))
        return(-1);

    errno = 0;
    bytes = fwrite(buffer, 1, len, file);
    if (bytes < (size_t) len)
        return(-xmlIOErr(errno));

    return(len);
}
#endif /* LIBXML_OUTPUT_ENABLED */

/**
 * 刷新I/O通道
 *
 * @param context  I/O上下文
 */
static int
xmlFileFlush (void * context) {
    FILE *file = context;

    if (file == NULL)
        return(-1);

    if (fflush(file) != 0)
        return(xmlIOErr(errno));

    return(XML_ERR_OK);
}

/**
 * @deprecated 内部函数，请勿使用。
 *
 * @param context  I/O上下文
 * @returns 0或-1，错误时返回错误代码
 */
int
xmlFileClose (void * context) {
    FILE *file = context;

    if (context == NULL)
        return(-1);

    if (file == stdin)
        return(0);
    if ((file == stdout) || (file == stderr))
        return(xmlFileFlush(file));

    if (fclose(file) != 0)
        return(xmlIOErr(errno));

    return(0);
}

#ifdef LIBXML_OUTPUT_ENABLED
/**
 * 将`buffer`中的`len`字节写入xml缓冲区
 *
 * @param context  xmlBuffer
 * @param buffer  要写入的数据
 * @param len  要写入的字节数
 * @returns 写入的字节数或负的xmlParserErrors
 * 值。
 */
static int
xmlBufferWrite (void * context, const char * buffer, int len) {
    int ret;

    ret = xmlBufferAdd((xmlBufferPtr) context, (const xmlChar *) buffer, len);
    if (ret != 0)
        return(-XML_ERR_NO_MEMORY);
    return(len);
}
#endif

#ifdef LIBXML_ZLIB_ENABLED
/************************************************************************
 *									*
 *		压缩文件访问的I/O			*
 *									*
 ************************************************************************/

/**
 * 从压缩I/O通道读取`len`字节到`buffer`。
 *
 * @param context  I/O上下文
 * @param buffer  数据存放位置
 * @param len  要写入的字节数
 * @returns 读取的字节数。
 */
static int
xmlGzfileRead (void * context, char * buffer, int len) {
    int ret;

    ret = gzread((gzFile) context, &buffer[0], len);
    if (ret < 0)
        return(-XML_IO_UNKNOWN);
    return(ret);
}

#ifdef LIBXML_OUTPUT_ENABLED
/**
 * 将`buffer`中的`len`字节写入压缩I/O通道。
 *
 * @param context  I/O上下文
 * @param buffer  数据存放位置
 * @param len  要写入的字节数
 * @returns 写入的字节数
 */
static int
xmlGzfileWrite (void * context, const char * buffer, int len) {
    int ret;

    ret = gzwrite((gzFile) context, (char *) &buffer[0], len);
    if (ret < 0)
        return(-XML_IO_UNKNOWN);
    return(ret);
}
#endif /* LIBXML_OUTPUT_ENABLED */

/**
 * 关闭压缩I/O通道
 *
 * @param context  I/O上下文
 */
static int
xmlGzfileClose (void * context) {
    if (gzclose((gzFile) context) != Z_OK)
        return(XML_IO_UNKNOWN);
    return(0);
}
#endif /* LIBXML_ZLIB_ENABLED */

/************************************************************************
 *									*
 *		压缩文件访问的I/O			*
 *									*
 ************************************************************************/

#ifdef LIBXML_LZMA_ENABLED

#include "private/xzlib.h"

/**
 * 从压缩I/O通道读取`len`字节到`buffer`。
 *
 * @param context  I/O上下文
 * @param buffer  数据存放位置
 * @param len  要写入的字节数
 * @returns 写入的字节数
 */
static int
xmlXzfileRead (void * context, char * buffer, int len) {
    int ret;

    ret = __libxml2_xzread((xzFile) context, &buffer[0], len);
    if (ret < 0)
        return(-XML_IO_UNKNOWN);
    return(ret);
}

/**
 * 关闭压缩I/O通道
 *
 * @param context  I/O上下文
 */
static int
xmlXzfileClose (void * context) {
    if (__libxml2_xzclose((xzFile) context) != LZMA_OK)
        return(XML_IO_UNKNOWN);
    return(0);
}
#endif /* LIBXML_LZMA_ENABLED */

/************************************************************************
 *									*
 *			输入/输出缓冲区				*
 *									*
 ************************************************************************/

static int
xmlIODefaultMatch(const char *filename ATTRIBUTE_UNUSED) {
    return(1);
}

/**
 * 更新缓冲区以从`fd`读取。支持XML_INPUT_UNZIP
 * 标志。
 *
 * @param buf  解析器输入缓冲区
 * @param fd  文件描述符
 * @param flags  标志
 * @returns xmlParserErrors代码。
 */
xmlParserErrors
xmlInputFromFd(xmlParserInputBufferPtr buf, int fd,
               xmlParserInputFlags flags) {
    xmlFdIOCtxt *fdctxt;
    int copy;

    (void) flags;

#ifdef LIBXML_LZMA_ENABLED
    if (flags & XML_INPUT_UNZIP) {
        xzFile xzStream;
        off_t pos;

        pos = lseek(fd, 0, SEEK_CUR);

        copy = dup(fd);
        if (copy == -1)
            return(xmlIOErr(errno));

        xzStream = __libxml2_xzdopen("?", copy, "rb");

        if (xzStream == NULL) {
            close(copy);
        } else {
            int compressed = (__libxml2_xzcompressed(xzStream) > 0);

            if ((compressed) ||
                /* 如果不是gzip压缩，尝试回退 */
                (pos < 0) ||
                (lseek(fd, pos, SEEK_SET) < 0)) {
                /*
                 * 如果文件不可寻址，我们通过xzlib
                 * 管道传输未压缩的输入。
                 */
                buf->context = xzStream;
                buf->readcallback = xmlXzfileRead;
                buf->closecallback = xmlXzfileClose;
                buf->compressed = compressed;

                return(XML_ERR_OK);
            }

            xmlXzfileClose(xzStream);
        }
    }
#endif /* LIBXML_LZMA_ENABLED */

#ifdef LIBXML_ZLIB_ENABLED
    if (flags & XML_INPUT_UNZIP) {
        gzFile gzStream;
        off_t pos;

        pos = lseek(fd, 0, SEEK_CUR);

        copy = dup(fd);
        if (copy == -1)
            return(xmlIOErr(errno));

        gzStream = gzdopen(copy, "rb");

        if (gzStream == NULL) {
            close(copy);
        } else {
            int compressed = (gzdirect(gzStream) == 0);

            if ((compressed) ||
                /* 如果不是gzip压缩，尝试回退 */
                (pos < 0) ||
                (lseek(fd, pos, SEEK_SET) < 0)) {
                /*
                 * 如果文件不可寻址，我们通过zlib
                 * 管道传输未压缩的输入。
                 */
                buf->context = gzStream;
                buf->readcallback = xmlGzfileRead;
                buf->closecallback = xmlGzfileClose;
                buf->compressed = compressed;

                return(XML_ERR_OK);
            }

            xmlGzfileClose(gzStream);
        }
    }
#endif /* LIBXML_ZLIB_ENABLED */

    copy = dup(fd);
    if (copy == -1)
        return(xmlIOErr(errno));

    fdctxt = xmlMalloc(sizeof(*fdctxt));
    if (fdctxt == NULL) {
        close(copy);
        return(XML_ERR_NO_MEMORY);
    }
    fdctxt->fd = copy;

    buf->context = fdctxt;
    buf->readcallback = xmlFdRead;
    buf->closecallback = xmlFdClose;

    return(XML_ERR_OK);
}

/**
 * @param buf  要填充的输入缓冲区
 * @param filename  文件名或URI
 * @param flags  XML_INPUT标志
 * @returns xmlParserErrors代码。
 */
static xmlParserErrors
xmlInputDefaultOpen(xmlParserInputBufferPtr buf, const char *filename,
                    xmlParserInputFlags flags) {
    xmlParserErrors ret;
    int fd;

    if (!xmlFileMatch(filename))
        return(XML_IO_ENOENT);

    ret = xmlFdOpen(filename, 0, &fd);
    if (ret != XML_ERR_OK)
        return(ret);

    ret = xmlInputFromFd(buf, fd, flags);

    close(fd);

    return(ret);
}

#ifdef LIBXML_OUTPUT_ENABLED
/**
 * @param buf  要填充的输入缓冲区
 * @param filename  文件名或URI
 * @param compression  压缩级别或0
 * @returns xmlParserErrors代码。
 */
static xmlParserErrors
xmlOutputDefaultOpen(xmlOutputBufferPtr buf, const char *filename,
                     int compression) {
    xmlFdIOCtxt *fdctxt;
    int fd;

    (void) compression;

    if (!strcmp(filename, "-")) {
        fd = dup(STDOUT_FILENO);

        if (fd < 0)
            return(xmlIOErr(errno));
    } else {
        int ret;

        ret = xmlFdOpen(filename, /* write */ 1, &fd);
        if (ret != XML_ERR_OK)
            return(ret);
    }

#ifdef LIBXML_ZLIB_ENABLED
    if ((compression > 0) && (compression <= 9)) {
        gzFile gzStream;
        char mode[15];

        snprintf(mode, sizeof(mode), "wb%d", compression);
        gzStream = gzdopen(fd, mode);

        if (gzStream == NULL) {
            close(fd);
            return(XML_IO_UNKNOWN);
        }

        buf->context = gzStream;
        buf->writecallback = xmlGzfileWrite;
        buf->closecallback = xmlGzfileClose;

        return(XML_ERR_OK);
    }
#endif /* LIBXML_ZLIB_ENABLED */

    fdctxt = xmlMalloc(sizeof(*fdctxt));
    if (fdctxt == NULL) {
        close(fd);
        return(XML_ERR_NO_MEMORY);
    }
    fdctxt->fd = fd;

    buf->context = fdctxt;
    buf->writecallback = xmlFdWrite;
    buf->closecallback = xmlFdClose;
    return(XML_ERR_OK);
}
#endif

/**
 * 为渐进式解析创建缓冲解析器输入。
 *
 * @deprecated 使用xmlNewInputFrom*。
 *
 * encoding参数已弃用，应设置为
 * XML_CHAR_ENCODING_NONE。编码可以稍后通过
 * xmlSwitchEncoding()或xmlSwitchEncodingName()更改。
 *
 * @param enc  已知的字符集编码（已弃用）
 * @returns 新的解析器输入或NULL
 */
xmlParserInputBufferPtr
xmlAllocParserInputBuffer(xmlCharEncoding enc) {
    xmlParserInputBufferPtr ret;

    ret = (xmlParserInputBufferPtr) xmlMalloc(sizeof(xmlParserInputBuffer));
    if (ret == NULL) {
	return(NULL);
    }
    memset(ret, 0, sizeof(xmlParserInputBuffer));
    ret->buffer = xmlBufCreate(XML_IO_BUFFER_SIZE);
    if (ret->buffer == NULL) {
        xmlFree(ret);
	return(NULL);
    }
    if (enc != XML_CHAR_ENCODING_NONE) {
        if (xmlLookupCharEncodingHandler(enc, &ret->encoder) != XML_ERR_OK) {
            /* 我们无法在这里正确处理错误。 */
            xmlFreeParserInputBuffer(ret);
            return(NULL);
        }
    }
    if (ret->encoder != NULL)
        ret->raw = xmlBufCreate(XML_IO_BUFFER_SIZE);
    else
        ret->raw = NULL;
    ret->readcallback = NULL;
    ret->closecallback = NULL;
    ret->context = NULL;
    ret->compressed = -1;
    ret->rawconsumed = 0;

    return(ret);
}

#ifdef LIBXML_OUTPUT_ENABLED
/**
 * 创建缓冲解析器输出
 *
 * 即使在错误情况下也会消耗`encoder`。
 *
 * @param encoder  编码转换器或NULL
 * @returns 新的解析器输出或NULL
 */
xmlOutputBufferPtr
xmlAllocOutputBuffer(xmlCharEncodingHandlerPtr encoder) {
    xmlOutputBufferPtr ret;

    ret = (xmlOutputBufferPtr) xmlMalloc(sizeof(xmlOutputBuffer));
    if (ret == NULL) {
        xmlCharEncCloseFunc(encoder);
	return(NULL);
    }
    memset(ret, 0, sizeof(xmlOutputBuffer));
    ret->buffer = xmlBufCreate(MINLEN);
    if (ret->buffer == NULL) {
        xmlCharEncCloseFunc(encoder);
        xmlFree(ret);
	return(NULL);
    }

    ret->encoder = encoder;
    if (encoder != NULL) {
        ret->conv = xmlBufCreate(MINLEN);
	if (ret->conv == NULL) {
            xmlOutputBufferClose(ret);
	    return(NULL);
	}

	/*
	 * 此调用旨在初始化编码器状态
	 */
	xmlCharEncOutput(ret, 1);
    } else
        ret->conv = NULL;
    ret->writecallback = NULL;
    ret->closecallback = NULL;
    ret->context = NULL;
    ret->written = 0;

    return(ret);
}
#endif /* LIBXML_OUTPUT_ENABLED */

/**
 * 释放缓冲解析器输入使用的内存
 *
 * @param in  缓冲解析器输入
 */
void
xmlFreeParserInputBuffer(xmlParserInputBufferPtr in) {
    if (in == NULL) return;

    if (in->raw) {
        xmlBufFree(in->raw);
	in->raw = NULL;
    }
    if (in->encoder != NULL) {
        xmlCharEncCloseFunc(in->encoder);
    }
    if (in->closecallback != NULL) {
	in->closecallback(in->context);
    }
    if (in->buffer != NULL) {
        xmlBufFree(in->buffer);
	in->buffer = NULL;
    }

    xmlFree(in);
}

#ifdef LIBXML_OUTPUT_ENABLED
/**
 * 刷新并关闭输出I/O通道
 * 并释放所有相关资源
 *
 * @param out  缓冲输出
 * @returns 写入的字节数或错误时的负xmlParserErrors
 * 代码。
 */
int
xmlOutputBufferClose(xmlOutputBufferPtr out)
{
    int ret;

    if (out == NULL)
        return (-1);

    if (out->writecallback != NULL)
        xmlOutputBufferFlush(out);

    if (out->closecallback != NULL) {
        int code = out->closecallback(out->context);

        if ((code != XML_ERR_OK) &&
            (!xmlIsCatastrophicError(XML_ERR_FATAL, out->error))) {
            if (code < 0)
                out->error = XML_IO_UNKNOWN;
            else
                out->error = code;
        }
    }

    if (out->error != XML_ERR_OK)
        ret = -out->error;
    else
        ret = out->written;

    if (out->conv) {
        xmlBufFree(out->conv);
        out->conv = NULL;
    }
    if (out->encoder != NULL) {
        xmlCharEncCloseFunc(out->encoder);
    }
    if (out->buffer != NULL) {
        xmlBufFree(out->buffer);
        out->buffer = NULL;
    }

    xmlFree(out);

    return(ret);
}
#endif /* LIBXML_OUTPUT_ENABLED */

/**
 * @param URI  文件名或URI
 * @param enc  编码枚举（已弃用）
 * @param flags  XML_INPUT标志
 * @param out  指向结果输入缓冲区的指针
 * @returns xmlParserErrors代码。
 */
xmlParserErrors
xmlParserInputBufferCreateUrl(const char *URI, xmlCharEncoding enc,
                              xmlParserInputFlags flags,
                              xmlParserInputBufferPtr *out) {
    xmlParserInputBufferPtr buf;
    xmlParserErrors ret;
    int i;

    xmlInitParser();

    *out = NULL;
    if (URI == NULL)
        return(XML_ERR_ARGUMENT);

    /*
     * 分配输入缓冲区前端。
     */
    buf = xmlAllocParserInputBuffer(enc);
    if (buf == NULL)
        return(XML_ERR_NO_MEMORY);

    /*
     * 尝试找到接受该方案的输入接受方法之一
     * 反向进行以优先考虑用户定义的处理程序。
     */
    ret = XML_IO_ENOENT;
    for (i = xmlInputCallbackNr - 1; i >= 0; i--) {
        xmlInputCallback *cb = &xmlInputCallbackTable[i];

        if (cb->matchcallback == xmlIODefaultMatch) {
            ret = xmlInputDefaultOpen(buf, URI, flags);

            if ((ret == XML_ERR_OK) || (ret != XML_IO_ENOENT))
                break;
        } else if ((cb->matchcallback != NULL) &&
                   (cb->matchcallback(URI) != 0)) {
            buf->context = cb->opencallback(URI);
            if (buf->context != NULL) {
                buf->readcallback = cb->readcallback;
                buf->closecallback = cb->closecallback;
                ret = XML_ERR_OK;
                break;
            }
        }
    }
    if (ret != XML_ERR_OK) {
        xmlFreeParserInputBuffer(buf);
        *out = NULL;
	return(ret);
    }

    *out = buf;
    return(ret);
}

xmlParserInputBufferPtr
__xmlParserInputBufferCreateFilename(const char *URI, xmlCharEncoding enc) {
    xmlParserInputBufferPtr ret;

    xmlParserInputBufferCreateUrl(URI, enc, 0, &ret);
    return(ret);
}

/**
 * 为文件的渐进式解析创建缓冲解析器输入
 * 如果在编译时找到，默认提供对ZLIB/Compress压缩文档的
 * 自动支持。
 * 如果enc == XML_CHAR_ENCODING_NONE，进行编码检查
 *
 * @deprecated 使用xmlNewInputFromUrl()。
 *
 * @param URI  包含URI或文件名的C字符串
 * @param enc  已知的字符集编码
 * @returns 新的解析器输入或NULL
 */
xmlParserInputBufferPtr
xmlParserInputBufferCreateFilename(const char *URI, xmlCharEncoding enc) {
    xmlParserInputBufferPtr ret;
    xmlParserErrors code;

    if (xmlParserInputBufferCreateFilenameValue != NULL)
        return(xmlParserInputBufferCreateFilenameValue(URI, enc));

    code = xmlParserInputBufferCreateUrl(URI, enc, 0, &ret);

    /*
     * xmlParserInputBufferCreateFilename无法返回
     * 错误类型，尽管这确实很关键。
     * 我们所能做的就是设置全局错误。
     */
    if ((code != XML_ERR_OK) && (code != XML_IO_ENOENT)) {
        if (xmlRaiseError(NULL, NULL, NULL, NULL, NULL, XML_FROM_IO, code,
                          XML_ERR_ERROR, URI, 0, NULL, NULL, NULL, 0, 0,
                          "Failed to open file\n") < 0)
            xmlRaiseMemoryError(NULL, NULL, NULL, XML_FROM_IO, NULL);
    }

    return(ret);
}

#ifdef LIBXML_OUTPUT_ENABLED
xmlOutputBufferPtr
__xmlOutputBufferCreateFilename(const char *URI,
                              xmlCharEncodingHandlerPtr encoder,
                              int compression) {
    xmlOutputBufferPtr ret = NULL;
    xmlURIPtr puri;
    int i = 0;
    char *unescaped = NULL;

    xmlInitParser();

    if (URI == NULL)
        goto error;

    puri = xmlParseURI(URI);
    if (puri != NULL) {
        /*
         * 尝试限制URI反转义代码的损害。
         */
        if (puri->scheme == NULL) {
            unescaped = xmlURIUnescapeString(URI, 0, NULL);
            if (unescaped == NULL) {
                xmlFreeURI(puri);
                goto error;
            }
            URI = unescaped;
        }
        xmlFreeURI(puri);
    }

    /*
     * 分配输出缓冲区前端。
     */
    ret = xmlAllocOutputBuffer(encoder);
    encoder = NULL;
    if (ret == NULL)
        goto error;

    /*
     * 尝试找到接受该方案的输出接受方法之一
     * 反向进行以优先考虑用户定义的处理程序。
     */
    for (i = xmlOutputCallbackNr - 1; i >= 0; i--) {
        xmlOutputCallback *cb = &xmlOutputCallbackTable[i];
        xmlParserErrors code;

        if (cb->matchcallback == xmlIODefaultMatch) {
            code = xmlOutputDefaultOpen(ret, URI, compression);
            /*  处理其他错误 */
            if (code == XML_ERR_OK)
                break;
        } else if ((cb->matchcallback != NULL) &&
                   (cb->matchcallback(URI) != 0)) {
            ret->context = cb->opencallback(URI);
            if (ret->context != NULL) {
                ret->writecallback = cb->writecallback;
                ret->closecallback = cb->closecallback;
                break;
            }
        }
    }

    if (ret->context == NULL) {
        xmlOutputBufferClose(ret);
	ret = NULL;
    }

error:
    xmlFree(unescaped);
    if (encoder != NULL)
        xmlCharEncCloseFunc(encoder);
    return(ret);
}

/**
 * 为文件的渐进式保存创建缓冲输出
 * 如果文件名是`"-"`，则我们使用stdout作为输出。
 * 如果在编译时找到，默认提供对ZLIB/Compress压缩文档的
 * 自动支持。
 *
 * 即使在错误情况下也会消耗`encoder`。
 *
 * @param URI  包含URI或文件名的C字符串
 * @param encoder  编码转换器或NULL
 * @param compression  压缩比率（0无，9最大）。
 * @returns 新的输出或NULL
 */
xmlOutputBufferPtr
xmlOutputBufferCreateFilename(const char *URI,
                              xmlCharEncodingHandlerPtr encoder,
                              int compression ATTRIBUTE_UNUSED) {
    if ((xmlOutputBufferCreateFilenameValue)) {
		return xmlOutputBufferCreateFilenameValue(URI, encoder, compression);
	}
	return __xmlOutputBufferCreateFilename(URI, encoder, compression);
}
#endif /* LIBXML_OUTPUT_ENABLED */

/**
 * 为FILE *缓冲C I/O的渐进式解析创建缓冲解析器输入
 *
 * @deprecated 请勿使用。
 *
 * encoding参数已弃用，应设置为
 * XML_CHAR_ENCODING_NONE。编码可以稍后通过
 * xmlSwitchEncoding()或xmlSwitchEncodingName()更改。
 *
 * @param file  FILE*
 * @param enc  已知的字符集编码（已弃用）
 * @returns 新的解析器输入或NULL
 */
xmlParserInputBufferPtr
xmlParserInputBufferCreateFile(FILE *file, xmlCharEncoding enc) {
    xmlParserInputBufferPtr ret;

    if (file == NULL) return(NULL);

    ret = xmlAllocParserInputBuffer(enc);
    if (ret != NULL) {
        ret->context = file;
	ret->readcallback = xmlFileRead;
	ret->closecallback = NULL;
    }

    return(ret);
}

#ifdef LIBXML_OUTPUT_ENABLED
/**
 * 为渐进式保存到`FILE *`缓冲C I/O创建缓冲输出。
 *
 * 即使在错误情况下也会消耗`encoder`。
 *
 * @param file  `FILE *`
 * @param encoder  编码转换器或NULL
 * @returns 新的解析器输出或NULL
 */
xmlOutputBufferPtr
xmlOutputBufferCreateFile(FILE *file, xmlCharEncodingHandlerPtr encoder) {
    xmlOutputBufferPtr ret;

    if (file == NULL) {
        xmlCharEncCloseFunc(encoder);
        return(NULL);
    }

    ret = xmlAllocOutputBuffer(encoder);
    if (ret != NULL) {
        ret->context = file;
	ret->writecallback = xmlFileWrite;
	ret->closecallback = xmlFileFlush;
    }

    return(ret);
}

/**
 * 为渐进式保存到xmlBuffer创建缓冲输出
 *
 * 即使在错误情况下也会消耗`encoder`。
 *
 * @param buffer  xmlBufferPtr
 * @param encoder  编码转换器或NULL
 * @returns 新的解析器输出或NULL
 */
xmlOutputBufferPtr
xmlOutputBufferCreateBuffer(xmlBufferPtr buffer,
                            xmlCharEncodingHandlerPtr encoder) {
    xmlOutputBufferPtr ret;

    if (buffer == NULL) {
        xmlCharEncCloseFunc(encoder);
        return(NULL);
    }

    ret = xmlOutputBufferCreateIO(xmlBufferWrite, NULL, (void *) buffer,
                                  encoder);

    return(ret);
}

/**
 * 返回指向输出缓冲区中当前保存数据的指针
 *
 * @param out  xmlOutputBufferPtr
 * @returns 指向数据的指针，错误时返回NULL
 */
const xmlChar *
xmlOutputBufferGetContent(xmlOutputBufferPtr out) {
    if ((out == NULL) || (out->buffer == NULL) || (out->error != 0))
        return(NULL);

    return(xmlBufContent(out->buffer));
}

/**
 * 返回输出缓冲区中当前保存数据的长度
 *
 * @param out  xmlOutputBufferPtr
 * @returns 错误或没有数据时返回0，否则返回大小
 */
size_t
xmlOutputBufferGetSize(xmlOutputBufferPtr out) {
    if ((out == NULL) || (out->buffer == NULL) || (out->error != 0))
        return(0);

    return(xmlBufUse(out->buffer));
}


#endif /* LIBXML_OUTPUT_ENABLED */

/**
 * 为来自文件描述符的输入的渐进式解析创建缓冲解析器输入
 *
 * @deprecated 使用xmlNewInputFromFd()。
 *
 * encoding参数已弃用，应设置为
 * XML_CHAR_ENCODING_NONE。编码可以稍后通过
 * xmlSwitchEncoding()或xmlSwitchEncodingName()更改。
 *
 * @param fd  文件描述符号
 * @param enc  已知的字符集编码（已弃用）
 * @returns 新的解析器输入或NULL
 */
xmlParserInputBufferPtr
xmlParserInputBufferCreateFd(int fd, xmlCharEncoding enc) {
    xmlParserInputBufferPtr ret;

    if (fd < 0) return(NULL);

    ret = xmlAllocParserInputBuffer(enc);
    if (ret != NULL) {
        xmlFdIOCtxt *fdctxt;

        fdctxt = xmlMalloc(sizeof(*fdctxt));
        if (fdctxt == NULL) {
            return(NULL);
        }
        fdctxt->fd = fd;

        ret->context = fdctxt;
	ret->readcallback = xmlFdRead;
        ret->closecallback = xmlFdFree;
    }

    return(ret);
}

typedef struct {
    const char *cur;
    size_t size;
} xmlMemIOCtxt;

static int
xmlMemRead(void *vctxt, char *buf, int size) {
    xmlMemIOCtxt *ctxt = vctxt;

    if ((size_t) size > ctxt->size)
        size = ctxt->size;

    memcpy(buf, ctxt->cur, size);
    ctxt->cur += size;
    ctxt->size -= size;

    return size;
}

static int
xmlMemClose(void *vctxt) {
    xmlMemIOCtxt *ctxt = vctxt;

    xmlFree(ctxt);
    return(0);
}

/**
 * 为内存创建输入缓冲区。
 *
 * @param mem  内存缓冲区
 * @param size  缓冲区大小
 * @param flags  标志
 * @param enc  已知的字符集编码（已弃用）
 * @returns 新的输入缓冲区或NULL。
 */
xmlParserInputBufferPtr
xmlNewInputBufferMemory(const void *mem, size_t size,
                        xmlParserInputFlags flags, xmlCharEncoding enc) {
    xmlParserInputBufferPtr ret;

    if ((flags & XML_INPUT_BUF_STATIC) &&
        ((flags & XML_INPUT_BUF_ZERO_TERMINATED) == 0)) {
        xmlMemIOCtxt *ctxt;

        /*
         * 没有零终止符的静态缓冲区。
         * 流式内存以避免复制。
         */
        ret = xmlAllocParserInputBuffer(enc);
        if (ret == NULL)
            return(NULL);

        ctxt = xmlMalloc(sizeof(*ctxt));
        if (ctxt == NULL) {
            xmlFreeParserInputBuffer(ret);
            return(NULL);
        }

        ctxt->cur = mem;
        ctxt->size = size;

        ret->context = ctxt;
        ret->readcallback = xmlMemRead;
        ret->closecallback = xmlMemClose;
    } else {
        ret = xmlMalloc(sizeof(*ret));
        if (ret == NULL)
            return(NULL);
        memset(ret, 0, sizeof(xmlParserInputBuffer));
        ret->compressed = -1;

        ret->buffer = xmlBufCreateMem((const xmlChar *) mem, size,
                                      (flags & XML_INPUT_BUF_STATIC ? 1 : 0));
        if (ret->buffer == NULL) {
            xmlFree(ret);
            return(NULL);
        }
    }

    return(ret);
}

/**
 * 为从内存区域解析创建解析器输入缓冲区。
 *
 * @deprecated 使用xmlNewInputFromMemory()。
 *
 * 此函数复制整个输入缓冲区。如果您确定
 * 缓冲区的内容在文档解析完成之前保持有效，
 * 您可以通过使用xmlParserInputBufferCreateStatic()
 * 来避免复制。
 *
 * encoding参数已弃用，应设置为
 * XML_CHAR_ENCODING_NONE。编码可以稍后通过
 * xmlSwitchEncoding()或xmlSwitchEncodingName()更改。
 *
 * @param mem  内存输入
 * @param size  内存块的长度
 * @param enc  已知的字符集编码（已弃用）
 * @returns 新的解析器输入，错误时返回NULL。
 */
xmlParserInputBufferPtr
xmlParserInputBufferCreateMem(const char *mem, int size, xmlCharEncoding enc) {
    if ((mem == NULL) || (size < 0))
        return(NULL);

    return(xmlNewInputBufferMemory(mem, size, 0, enc));
}

/**
 * 为从内存区域解析创建解析器输入缓冲区。
 *
 * @deprecated 使用xmlNewInputFromMemory()。
 *
 * 此函数假设输入缓冲区的内容在文档解析完成之前
 * 保持有效。否则使用xmlParserInputBufferCreateMem()。
 *
 * encoding参数已弃用，应设置为
 * XML_CHAR_ENCODING_NONE。编码可以稍后通过
 * xmlSwitchEncoding()或xmlSwitchEncodingName()更改。
 *
 * @param mem  内存输入
 * @param size  内存块的长度
 * @param enc  已知的字符集编码
 * @returns 新的解析器输入，错误时返回NULL。
 */
xmlParserInputBufferPtr
xmlParserInputBufferCreateStatic(const char *mem, int size,
                                 xmlCharEncoding enc) {
    if ((mem == NULL) || (size < 0))
        return(NULL);

    return(xmlNewInputBufferMemory(mem, size, XML_INPUT_BUF_STATIC, enc));
}

/**
 * 为以null结尾的C字符串创建输入缓冲区。
 *
 * @deprecated 使用xmlNewInputFromString()。
 *
 * @param str  C字符串
 * @param flags  标志
 * @returns 新的输入缓冲区或NULL。
 */
xmlParserInputBufferPtr
xmlNewInputBufferString(const char *str, xmlParserInputFlags flags) {
    xmlParserInputBufferPtr ret;

    ret = xmlMalloc(sizeof(*ret));
    if (ret == NULL)
	return(NULL);
    memset(ret, 0, sizeof(xmlParserInputBuffer));
    ret->compressed = -1;

    ret->buffer = xmlBufCreateMem((const xmlChar *) str, strlen(str),
                                  (flags & XML_INPUT_BUF_STATIC ? 1 : 0));
    if (ret->buffer == NULL) {
        xmlFree(ret);
	return(NULL);
    }

    return(ret);
}

#ifdef LIBXML_OUTPUT_ENABLED
/**
 * 为渐进式保存到文件描述符创建缓冲输出
 *
 * 即使在错误情况下也会消耗`encoder`。
 *
 * @param fd  文件描述符号
 * @param encoder  编码转换器或NULL
 * @returns 新的解析器输出或NULL
 */
xmlOutputBufferPtr
xmlOutputBufferCreateFd(int fd, xmlCharEncodingHandlerPtr encoder) {
    xmlOutputBufferPtr ret;

    if (fd < 0) {
        xmlCharEncCloseFunc(encoder);
        return(NULL);
    }

    ret = xmlAllocOutputBuffer(encoder);
    if (ret != NULL) {
        xmlFdIOCtxt *fdctxt;

        fdctxt = xmlMalloc(sizeof(*fdctxt));
        if (fdctxt == NULL) {
            return(NULL);
        }
        fdctxt->fd = fd;

        ret->context = fdctxt;
	ret->writecallback = xmlFdWrite;
        ret->closecallback = xmlFdFree;
    }

    return(ret);
}
#endif /* LIBXML_OUTPUT_ENABLED */

/**
 * 为来自I/O处理程序的输入的渐进式解析创建缓冲解析器输入
 *
 * @deprecated 使用xmlNewInputFromIO()。
 *
 * encoding参数已弃用，应设置为
 * XML_CHAR_ENCODING_NONE。编码可以稍后通过
 * xmlSwitchEncoding()或xmlSwitchEncodingName()更改。
 *
 * @param ioread  I/O读取函数
 * @param ioclose  I/O关闭函数
 * @param ioctx  I/O处理程序
 * @param enc  已知的字符集编码（已弃用）
 * @returns 新的解析器输入或NULL
 */
xmlParserInputBufferPtr
xmlParserInputBufferCreateIO(xmlInputReadCallback   ioread,
	 xmlInputCloseCallback  ioclose, void *ioctx, xmlCharEncoding enc) {
    xmlParserInputBufferPtr ret;

    if (ioread == NULL) return(NULL);

    ret = xmlAllocParserInputBuffer(enc);
    if (ret != NULL) {
        ret->context = (void *) ioctx;
	ret->readcallback = ioread;
	ret->closecallback = ioclose;
    }

    return(ret);
}

#ifdef LIBXML_OUTPUT_ENABLED
/**
 * 为渐进式保存到I/O处理程序创建缓冲输出
 *
 * 即使在错误情况下也会消耗`encoder`。
 *
 * @param iowrite  I/O写入函数
 * @param ioclose  I/O关闭函数
 * @param ioctx  I/O处理程序
 * @param encoder  已知的字符集编码
 * @returns 新的解析器输出或NULL
 */
xmlOutputBufferPtr
xmlOutputBufferCreateIO(xmlOutputWriteCallback   iowrite,
	 xmlOutputCloseCallback  ioclose, void *ioctx,
	 xmlCharEncodingHandlerPtr encoder) {
    xmlOutputBufferPtr ret;

    if (iowrite == NULL) {
        xmlCharEncCloseFunc(encoder);
        return(NULL);
    }

    ret = xmlAllocOutputBuffer(encoder);
    if (ret != NULL) {
        ret->context = (void *) ioctx;
	ret->writecallback = iowrite;
	ret->closecallback = ioclose;
    }

    return(ret);
}
#endif /* LIBXML_OUTPUT_ENABLED */

/**
 * 为URI输入文件处理注册回调
 *
 * @deprecated 使用xmlCtxtSetResourceLoader()或类似函数。
 *
 * @param func  指向新ParserInputBufferCreateFilenameFunc的函数指针
 * @returns 注册函数的旧值
 */
xmlParserInputBufferCreateFilenameFunc
xmlParserInputBufferCreateFilenameDefault(
        xmlParserInputBufferCreateFilenameFunc func)
{
    xmlParserInputBufferCreateFilenameFunc old;

    old = xmlParserInputBufferCreateFilenameValue;
    if (old == NULL)
        old = __xmlParserInputBufferCreateFilename;

    if (func == __xmlParserInputBufferCreateFilename)
        func = NULL;
    xmlParserInputBufferCreateFilenameValue = func;
    return(old);
}

/**
 * 为URI输出文件处理注册回调
 *
 * @param func  指向新OutputBufferCreateFilenameFunc的函数指针
 * @returns 注册函数的旧值
 */
xmlOutputBufferCreateFilenameFunc
xmlOutputBufferCreateFilenameDefault(xmlOutputBufferCreateFilenameFunc func)
{
    xmlOutputBufferCreateFilenameFunc old = xmlOutputBufferCreateFilenameValue;
#ifdef LIBXML_OUTPUT_ENABLED
    if (old == NULL) {
		old = __xmlOutputBufferCreateFilename;
	}
#endif
    xmlOutputBufferCreateFilenameValue = func;
    return(old);
}

/**
 * 将数组内容推送到输入缓冲区
 * 此例程处理到内部UTF-8的I18N转码
 * 这在以渐进式（推送）模式操作解析器时使用。
 *
 * @deprecated 内部函数，请勿使用。
 *
 * @param in  缓冲解析器输入
 * @param len  数组的字节大小。
 * @param buf  字符数组
 * @returns 读取并存储在缓冲区中的字符数，或错误时返回-1。
 */
int
xmlParserInputBufferPush(xmlParserInputBufferPtr in,
	                 int len, const char *buf) {
    size_t nbchars = 0;
    int ret;

    if (len < 0) return(0);
    if ((in == NULL) || (in->error)) return(-1);
    if (in->encoder != NULL) {
        /*
	 * 将数据存储在传入的原始缓冲区中
	 */
        if (in->raw == NULL) {
	    in->raw = xmlBufCreate(50);
            if (in->raw == NULL) {
                in->error = XML_ERR_NO_MEMORY;
                return(-1);
            }
	}
	ret = xmlBufAdd(in->raw, (const xmlChar *) buf, len);
	if (ret != 0) {
            in->error = XML_ERR_NO_MEMORY;
	    return(-1);
        }

	/*
	 * 尽可能多地转换到解析器读取缓冲区。
	 */
        nbchars = SIZE_MAX;
	if (xmlCharEncInput(in, &nbchars, /* flush */ 0) !=
            XML_ENC_ERR_SUCCESS)
            return(-1);
        if (nbchars > INT_MAX)
            nbchars = INT_MAX;
    } else {
	nbchars = len;
        ret = xmlBufAdd(in->buffer, (xmlChar *) buf, nbchars);
	if (ret != 0) {
            in->error = XML_ERR_NO_MEMORY;
	    return(-1);
        }
    }
    return(nbchars);
}

/*
 * 当从输入通道读取时指示文件结束或错误
 * 不要再次从中重新读取。
 */
static int
endOfInput (void * context ATTRIBUTE_UNUSED,
	    char * buffer ATTRIBUTE_UNUSED,
	    int len ATTRIBUTE_UNUSED) {
    return(0);
}

/**
 * 增长输入缓冲区的内容，保留旧数据
 * 此例程处理到内部UTF-8的I18N转码
 * 此例程在以正常（拉取）模式操作解析器时使用
 *
 * @deprecated 内部函数，请勿使用。
 *
 * @param in  缓冲解析器输入
 * @param len  要读取的字符数量的指示值
 * @returns 读取并存储在缓冲区中的字符数，或错误时返回-1。
 */
int
xmlParserInputBufferGrow(xmlParserInputBufferPtr in, int len) {
    int res = 0;

    if ((in == NULL) || (in->error))
        return(-1);

    if (len < MINLEN)
        len = MINLEN;

    /*
     * 调用此I/O类型的读取方法。
     */
    if (in->readcallback != NULL) {
        xmlBufPtr buf;

        if (in->encoder == NULL) {
            buf = in->buffer;
        } else {
            /*
             * 一些用户只设置'encoder'并期望我们
             * 延迟创建原始缓冲区。
             */
            if (in->raw == NULL) {
                in->raw = xmlBufCreate(XML_IO_BUFFER_SIZE);
                if (in->raw == NULL) {
                    in->error = XML_ERR_NO_MEMORY;
                    return(-1);
                }
            }
            buf = in->raw;
        }

        if (xmlBufGrow(buf, len) < 0) {
            in->error = XML_ERR_NO_MEMORY;
            return(-1);
        }

	res = in->readcallback(in->context, (char *)xmlBufEnd(buf), len);
	if (res <= 0)
	    in->readcallback = endOfInput;
        if (res < 0) {
            if (res == -1)
                in->error = XML_IO_UNKNOWN;
            else
                in->error = -res;
            return(-1);
        }

        if (xmlBufAddLen(buf, res) < 0) {
            in->error = XML_ERR_NO_MEMORY;
            return(-1);
        }
    }

    /*
     * 处理编码。
     */
    if (in->encoder != NULL) {
        size_t sizeOut;

        /*
         * 从内存读取时不要转换整个缓冲区。
         */
        if (in->readcallback == NULL)
            sizeOut = len;
        else
            sizeOut = SIZE_MAX;

	if (xmlCharEncInput(in, &sizeOut, /* flush */ 0) !=
            XML_ENC_ERR_SUCCESS)
	    return(-1);
        res = sizeOut;
    }
    return(res);
}

/**
 * 与xmlParserInputBufferGrow()相同。
 *
 * @deprecated 内部函数，请勿使用。
 *
 * @param in  缓冲解析器输入
 * @param len  要读取的字符数量的指示值
 * @returns 读取并存储在缓冲区中的字符数，或错误时返回-1。
 */
int
xmlParserInputBufferRead(xmlParserInputBufferPtr in, int len) {
    return(xmlParserInputBufferGrow(in, len));
}

#ifdef LIBXML_OUTPUT_ENABLED
/**
 * 将数组内容写入输出I/O缓冲区
 * 此例程处理从内部UTF-8的I18N转码
 * 缓冲区是无损的，即在部分或延迟写入的情况下会存储。
 *
 * @param out  缓冲解析器输出
 * @param len  数组的字节大小。
 * @param data  字符数组
 * @returns 立即写入的字符数，或错误时返回-1。
 */
int
xmlOutputBufferWrite(xmlOutputBufferPtr out, int len, const char *data) {
    xmlBufPtr buf = NULL;
    size_t written = 0;
    int ret;

    if ((out == NULL) || (out->error))
        return(-1);
    if (len < 0)
        return(0);

    ret = xmlBufAdd(out->buffer, (const xmlChar *) data, len);
    if (ret != 0) {
        out->error = XML_ERR_NO_MEMORY;
        return(-1);
    }

    /*
     * 首先处理编码内容。
     */
    if (out->encoder != NULL) {
        /*
         * 将数据存储在传入的原始缓冲区中
         */
        if (out->conv == NULL) {
            out->conv = xmlBufCreate(MINLEN);
            if (out->conv == NULL) {
                out->error = XML_ERR_NO_MEMORY;
                return(-1);
            }
        }

        /*
         * 尽可能多地转换到解析器读取缓冲区。
         */
        if (xmlBufUse(out->buffer) < 256) {
            ret = 0;
        } else {
            ret = xmlCharEncOutput(out, 0);
            if (ret < 0)
                return(-1);
        }

        if (out->writecallback)
            buf = out->conv;
        else
            written = ret;
    } else {
        if (out->writecallback)
            buf = out->buffer;
        else
            written = len;
    }

    if ((buf != NULL) && (out->writecallback)) {
        /*
         * 其次将内容写入I/O通道
         */
        while (1) {
            size_t nbchars = xmlBufUse(buf);

            if (nbchars < MINLEN)
                break;

            ret = out->writecallback(out->context,
                       (const char *)xmlBufContent(buf), nbchars);
            if (ret < 0) {
                out->error = (ret == -1) ? XML_IO_WRITE : -ret;
                return(-1);
            }
            if ((ret == 0) || ((size_t) ret > nbchars)) {
                out->error = XML_ERR_INTERNAL_ERROR;
                return(-1);
            }

            xmlBufShrink(buf, ret);
            written += ret;
            if (out->written > INT_MAX - ret)
                out->written = INT_MAX;
            else
                out->written += ret;
        }
    }

    return(written <= INT_MAX ? written : INT_MAX);
}

/**
 * 将字符串内容写入输出I/O缓冲区
 * 此例程转义字符，然后处理从内部UTF-8的I18N
 * 转码
 * 缓冲区是无损的，即在部分或延迟写入的情况下会存储。
 *
 * @param out  缓冲解析器输出
 * @param str  以零结尾的UTF-8字符串
 * @param escaping  可选的转义函数（或NULL）
 * @returns 立即写入的字符数，或错误时返回-1。
 */
int
xmlOutputBufferWriteEscape(xmlOutputBufferPtr out, const xmlChar *str,
                           xmlCharEncodingOutputFunc escaping) {
    int ret;
    int written = 0;
    size_t len;

    if ((out == NULL) || (out->error) || (str == NULL))
        return(-1);

    len = strlen((const char *) str);
    if (len >= INT_MAX) {
        out->error = XML_ERR_RESOURCE_LIMIT;
        return(-1);
    }

    if (escaping == NULL) {
        char *escaped = (char *) xmlEscapeText(str, XML_ESCAPE_ALLOW_INVALID);

        if (escaped == NULL) {
            out->error = XML_ERR_NO_MEMORY;
            return(-1);
        }

        len = strlen(escaped);
        if (len >= INT_MAX) {
            out->error = XML_ERR_RESOURCE_LIMIT;
            return(-1);
        }

        ret = xmlOutputBufferWrite(out, len, escaped);

        xmlFree(escaped);
        return(ret);
    }

    while (len > 0) {
        xmlChar buf[1024];
        int c_out;
        int c_in;

	c_out = 1024;
	c_in = len;

        ret = escaping(buf, &c_out, str, &c_in);
        if (ret < 0) {
            out->error = XML_ERR_NO_MEMORY;
            return(-1);
        }
        str += c_in;
        len -= c_in;

        ret = xmlOutputBufferWrite(out, c_out, (char *) buf);
        if (ret < 0)
            return(ret);
        written += ret;
    }

    return(written);
}

/**
 * 将字符串内容写入输出I/O缓冲区
 * 此例程处理从内部UTF-8的I18N转码
 * 缓冲区是无损的，即在部分或延迟写入的情况下会存储。
 *
 * @param out  缓冲解析器输出
 * @param str  以零结尾的C字符串
 * @returns 立即写入的字符数，或错误时返回-1。
 */
int
xmlOutputBufferWriteString(xmlOutputBufferPtr out, const char *str) {
    int len;

    if ((out == NULL) || (out->error)) return(-1);
    if (str == NULL)
        return(-1);
    len = strlen(str);

    if (len > 0)
	return(xmlOutputBufferWrite(out, len, str));
    return(len);
}

/**
 * 管理和增长输出缓冲区的例程。这个函数写入
 * 带引号或双引号的\#xmlChar字符串，首先检查它内部是否
 * 包含引号或双引号
 *
 * @param buf  输出缓冲区
 * @param string  要添加的字符串
 */
void
xmlOutputBufferWriteQuotedString(xmlOutputBufferPtr buf,
                                 const xmlChar *string) {
    const xmlChar *cur, *base;

    if ((buf == NULL) || (buf->error))
        return;

    if (xmlStrchr(string, '\"')) {
        if (xmlStrchr(string, '\'')) {
	    xmlOutputBufferWrite(buf, 1, "\"");
            base = cur = string;
            while(*cur != 0){
                if(*cur == '"'){
                    if (base != cur)
                        xmlOutputBufferWrite(buf, cur - base,
                                             (const char *) base);
                    xmlOutputBufferWrite(buf, 6, "&quot;");
                    cur++;
                    base = cur;
                }
                else {
                    cur++;
                }
            }
            if (base != cur)
                xmlOutputBufferWrite(buf, cur - base, (const char *) base);
	    xmlOutputBufferWrite(buf, 1, "\"");
	}
        else{
	    xmlOutputBufferWrite(buf, 1, "'");
            xmlOutputBufferWriteString(buf, (const char *) string);
	    xmlOutputBufferWrite(buf, 1, "'");
        }
    } else {
        xmlOutputBufferWrite(buf, 1, "\"");
        xmlOutputBufferWriteString(buf, (const char *) string);
        xmlOutputBufferWrite(buf, 1, "\"");
    }
}

/**
 * 刷新输出I/O通道
 *
 * @param out  缓冲输出
 * @returns 写入的字节数，错误时返回-1。
 */
int
xmlOutputBufferFlush(xmlOutputBufferPtr out) {
    int nbchars = 0, ret = 0;

    if ((out == NULL) || (out->error)) return(-1);
    /*
     * 首先处理编码内容。
     */
    if ((out->conv != NULL) && (out->encoder != NULL)) {
	/*
	 * 尽可能多地转换到解析器输出缓冲区。
	 */
	do {
	    nbchars = xmlCharEncOutput(out, 0);
	    if (nbchars < 0)
		return(-1);
	} while (nbchars);
    }

    /*
     * 其次将内容刷新到I/O通道
     */
    if ((out->conv != NULL) && (out->encoder != NULL) &&
	(out->writecallback != NULL)) {
	ret = out->writecallback(out->context,
                                 (const char *)xmlBufContent(out->conv),
                                 xmlBufUse(out->conv));
	if (ret >= 0)
	    xmlBufShrink(out->conv, ret);
    } else if (out->writecallback != NULL) {
	ret = out->writecallback(out->context,
                                 (const char *)xmlBufContent(out->buffer),
                                 xmlBufUse(out->buffer));
	if (ret >= 0)
	    xmlBufShrink(out->buffer, ret);
    }
    if (ret < 0) {
        out->error = (ret == -1) ? XML_IO_WRITE : -ret;
	return(ret);
    }
    if (out->written > INT_MAX - ret)
        out->written = INT_MAX;
    else
        out->written += ret;

    return(ret);
}
#endif /* LIBXML_OUTPUT_ENABLED */

/**
 * 查找该文件的目录
 *
 * @param filename  文件的路径
 * @returns 包含目录的新分配字符串，或NULL。
 */
char *
xmlParserGetDirectory(const char *filename) {
    char *ret = NULL;
    char dir[1024];
    char *cur;

    if (filename == NULL) return(NULL);

#if defined(_WIN32)
#   define IS_XMLPGD_SEP(ch) ((ch=='/')||(ch=='\\'))
#else
#   define IS_XMLPGD_SEP(ch) (ch=='/')
#endif

    strncpy(dir, filename, 1023);
    dir[1023] = 0;
    cur = &dir[strlen(dir)];
    while (cur > dir) {
         if (IS_XMLPGD_SEP(*cur)) break;
	 cur --;
    }
    if (IS_XMLPGD_SEP(*cur)) {
        if (cur == dir) dir[1] = 0;
	else *cur = 0;
	ret = xmlMemStrdup(dir);
    } else {
        ret = xmlMemStrdup(".");
    }
    return(ret);
#undef IS_XMLPGD_SEP
}

/**
 * 类似xmlCheckFilename()但处理文件URI。
 *
 * @deprecated 内部函数，请勿使用。
 *
 * @param filename  要检查的路径
 * @returns 0、1或2。
 */
int
xmlNoNetExists(const char *filename) {
    char *fromUri;
    int ret;

    if (filename == NULL)
	return(0);

    if (xmlConvertUriToPath(filename, &fromUri) < 0)
        return(0);

    if (fromUri != NULL)
        filename = fromUri;

    ret =  xmlCheckFilename(filename);

    xmlFree(fromUri);
    return(ret);
}

/************************************************************************
 *									*
 *			输入/输出回调				*
 *									*
 ************************************************************************/

/**
 * 初始化回调表。
 */
void
xmlInitIOCallbacks(void)
{
    xmlInputCallbackNr = 1;
    xmlInputCallbackTable[0].matchcallback = xmlIODefaultMatch;

#ifdef LIBXML_OUTPUT_ENABLED
    xmlOutputCallbackNr = 1;
    xmlOutputCallbackTable[0].matchcallback = xmlIODefaultMatch;
#endif
}

/**
 * 为处理解析器输入注册一组新的I/O回调。
 *
 * @deprecated 使用xmlCtxtSetResourceLoader()或类似函数。
 *
 * @param matchFunc  xmlInputMatchCallback
 * @param openFunc  xmlInputOpenCallback
 * @param readFunc  xmlInputReadCallback
 * @param closeFunc  xmlInputCloseCallback
 * @returns 注册的处理程序号，错误时返回-1
 */
int
xmlRegisterInputCallbacks(xmlInputMatchCallback matchFunc,
	xmlInputOpenCallback openFunc, xmlInputReadCallback readFunc,
	xmlInputCloseCallback closeFunc) {
    xmlInitParser();

    if (xmlInputCallbackNr >= MAX_INPUT_CALLBACK) {
	return(-1);
    }
    xmlInputCallbackTable[xmlInputCallbackNr].matchcallback = matchFunc;
    xmlInputCallbackTable[xmlInputCallbackNr].opencallback = openFunc;
    xmlInputCallbackTable[xmlInputCallbackNr].readcallback = readFunc;
    xmlInputCallbackTable[xmlInputCallbackNr].closecallback = closeFunc;
    return(xmlInputCallbackNr++);
}

/**
 * 注册默认的编译内置I/O处理程序。
 */
void
xmlRegisterDefaultInputCallbacks(void) {
    xmlRegisterInputCallbacks(xmlIODefaultMatch, NULL, NULL, NULL);
}

/**
 * 从输入堆栈中清除顶部输入回调。这包括
 * 编译内置的I/O。
 *
 * @returns 注册的输入回调数量，错误时返回-1。
 */
int
xmlPopInputCallbacks(void)
{
    xmlInitParser();

    if (xmlInputCallbackNr <= 0)
        return(-1);

    xmlInputCallbackNr--;

    return(xmlInputCallbackNr);
}

/**
 * 清除整个输入回调表。这包括
 * 编译内置的I/O。
 */
void
xmlCleanupInputCallbacks(void)
{
    xmlInitParser();

    xmlInputCallbackNr = 0;
}

#ifdef LIBXML_OUTPUT_ENABLED
/**
 * 为处理输出注册一组新的I/O回调。
 *
 * @param matchFunc  xmlOutputMatchCallback
 * @param openFunc  xmlOutputOpenCallback
 * @param writeFunc  xmlOutputWriteCallback
 * @param closeFunc  xmlOutputCloseCallback
 * @returns 注册的处理程序号，错误时返回-1
 */
int
xmlRegisterOutputCallbacks(xmlOutputMatchCallback matchFunc,
	xmlOutputOpenCallback openFunc, xmlOutputWriteCallback writeFunc,
	xmlOutputCloseCallback closeFunc) {
    xmlInitParser();

    if (xmlOutputCallbackNr >= MAX_OUTPUT_CALLBACK) {
	return(-1);
    }
    xmlOutputCallbackTable[xmlOutputCallbackNr].matchcallback = matchFunc;
    xmlOutputCallbackTable[xmlOutputCallbackNr].opencallback = openFunc;
    xmlOutputCallbackTable[xmlOutputCallbackNr].writecallback = writeFunc;
    xmlOutputCallbackTable[xmlOutputCallbackNr].closecallback = closeFunc;
    return(xmlOutputCallbackNr++);
}

/**
 * 注册默认的编译内置I/O处理程序。
 */
void
xmlRegisterDefaultOutputCallbacks (void) {
    xmlRegisterOutputCallbacks(xmlIODefaultMatch, NULL, NULL, NULL);
}

/**
 * 从输出堆栈中移除顶部输出回调。这包括
 * 编译内置的I/O。
 *
 * @returns 注册的输出回调数量，错误时返回-1。
 */
int
xmlPopOutputCallbacks(void)
{
    xmlInitParser();

    if (xmlOutputCallbackNr <= 0)
        return(-1);

    xmlOutputCallbackNr--;

    return(xmlOutputCallbackNr);
}

/**
 * 清除整个输出回调表。这包括
 * 编译内置的I/O回调。
 */
void
xmlCleanupOutputCallbacks(void)
{
    xmlInitParser();

    xmlOutputCallbackNr = 0;
}
#endif /* LIBXML_OUTPUT_ENABLED */

